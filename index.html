<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Introduction to Machine Learning - Study Hub</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #e0e0e0;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
            border-bottom: 2px solid #333;
        }

        h1 {
            font-size: 2.5rem;
            color: #64ffda;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .subtitle {
            color: #888;
            font-size: 1.1rem;
            font-weight: 300;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .module-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 25px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .module-card:hover {
            transform: translateY(-5px);
            border-color: #64ffda;
            box-shadow: 0 10px 30px rgba(100, 255, 218, 0.1);
        }

        .module-title {
            color: #64ffda;
            font-size: 1.3rem;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .topics-list {
            list-style: none;
            margin-bottom: 20px;
        }

        .topics-list li {
            padding: 5px 0;
            color: #ccc;
            font-size: 0.9rem;
        }

        .topics-list li:before {
            content: "▸ ";
            color: #64ffda;
            font-weight: bold;
        }

        .file-links {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }

        .file-link {
            display: inline-block;
            padding: 12px 20px;
            background: linear-gradient(45deg, rgba(0, 255, 255, 0.1), rgba(0, 255, 255, 0.2));
            border: 2px solid #00ffff;
            border-radius: 25px;
            color: #00ffff;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
        }

        .file-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .file-link:hover::before {
            left: 100%;
        }

        .file-link:hover {
            background: linear-gradient(45deg, #00ffff, rgba(0, 255, 255, 0.8));
            color: #000;
            transform: scale(1.1) translateY(-2px);
            box-shadow:
                0 0 25px rgba(0, 255, 255, 0.6),
                0 5px 15px rgba(0, 255, 255, 0.4);
            text-shadow: none;
        }

        .qb-link {
            background: linear-gradient(45deg, rgba(255, 0, 255, 0.1), rgba(255, 0, 255, 0.2));
            border-color: #ff00ff;
            color: #ff00ff;
            box-shadow: 0 0 15px rgba(255, 0, 255, 0.3);
        }

        .qb-link:hover {
            background: linear-gradient(45deg, #ff00ff, rgba(255, 0, 255, 0.8));
            color: #000;
            box-shadow:
                0 0 25px rgba(255, 0, 255, 0.6),
                0 5px 15px rgba(255, 0, 255, 0.4);
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
            padding: 25px;
            margin-top: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stats-title {
            color: #64ffda;
            font-size: 1.2rem;
            margin-bottom: 15px;
        }

        .priority-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .priority-item {
            background: rgba(100, 255, 218, 0.05);
            padding: 12px 16px;
            border-radius: 8px;
            border-left: 3px solid #64ffda;
        }

        .priority-rank {
            color: #64ffda;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .priority-topic {
            color: #fff;
            font-size: 0.95rem;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            border-top: 1px solid #333;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            h1 {
                font-size: 2rem;
            }

            .modules-grid {
                grid-template-columns: 1fr;
            }

            .file-links {
                flex-direction: column;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>Introduction to Machine Learning</h1>
            <p class="subtitle">BCA 4th Semester • Study Materials & Resources</p>
        </header>

        <div class="modules-grid">
            <!-- Module 1 -->
            <div class="module-card">
                <h2 class="module-title">Module 1: Introduction to ML</h2>
                <ul class="topics-list">
                    <li>What is Machine Learning?</li>
                    <li>Types of ML (Supervised, Unsupervised, Reinforcement)</li>
                    <li>Data Types & Preprocessing</li>
                    <li>Data Cleaning & Wrangling</li>
                    <li>Data Exploration & Visualization</li>
                </ul>
                <div class="file-links">
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Notes/Module_1_Introduction_to_ML.pdf"
                        class="file-link">📚 Notes</a>
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Question_Banks/QB_Module_1.pdf"
                        class="file-link qb-link">❓ Question Bank</a>
                </div>
            </div>

            <!-- Module 2 Part 1 -->
            <div class="module-card">
                <h2 class="module-title">Module 2: Supervised Learning - Part 1</h2>
                <ul class="topics-list">
                    <li>Linear Regression (Simple & Multiple)</li>
                    <li>Residual Analysis</li>
                    <li>R-squared & Adjusted R-squared</li>
                    <li>Feature Scaling & Normalization</li>
                    <li>Maximum Likelihood Estimation</li>
                </ul>
                <div class="file-links">
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Notes/Module_2_Part_1.pdf" class="file-link">📚 Notes</a>
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Question_Banks/QB_Module_2_Part_1.pdf"
                        class="file-link qb-link">❓ Question Bank</a>
                </div>
            </div>

            <!-- Module 2 Part 2 -->
            <div class="module-card">
                <h2 class="module-title">Module 2: Supervised Learning - Part 2</h2>
                <ul class="topics-list">
                    <li>Classification & Confusion Matrix</li>
                    <li>ROC Curve & Performance Metrics</li>
                    <li>Decision Trees & CART</li>
                    <li>Random Forest & Ensemble Methods</li>
                    <li>Support Vector Machines (SVM)</li>
                </ul>
                <div class="file-links">
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Notes/Module_2_Part_2.pdf" class="file-link">📚 Notes</a>
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Question_Banks/QB_Module_2_Part_2.pdf"
                        class="file-link qb-link">❓ Question Bank</a>
                </div>
            </div>

            <!-- Module 3 Part 1 -->
            <div class="module-card">
                <h2 class="module-title">Module 3: Unsupervised Learning - Part 1</h2>
                <ul class="topics-list">
                    <li>Clustering (K-Means & Hierarchical)</li>
                    <li>Distance Measures</li>
                    <li>Principal Component Analysis (PCA)</li>
                    <li>Eigenvectors & Eigenvalues</li>
                    <li>Dimensionality Reduction</li>
                </ul>
                <div class="file-links">
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Notes/Module_3_Part_1.pdf" class="file-link">📚 Notes</a>
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Question_Banks/QB_Module_3_Part_1.pdf"
                        class="file-link qb-link">❓ Question Bank</a>
                </div>
            </div>

            <!-- Module 3 Part 2 -->
            <div class="module-card">
                <h2 class="module-title">Module 3: Unsupervised Learning - Part 2</h2>
                <ul class="topics-list">
                    <li>Anomaly Detection</li>
                    <li>Recommendation Systems</li>
                    <li>Collaborative Filtering</li>
                    <li>Content-based Filtering</li>
                    <li>Matrix Factorization</li>
                </ul>
                <div class="file-links">
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Notes/Module_3_Part_2.pdf" class="file-link">📚 Notes</a>
                </div>
            </div>

            <!-- Module 4 -->
            <div class="module-card">
                <h2 class="module-title">Module 4: Advanced ML Concepts</h2>
                <ul class="topics-list">
                    <li>Model Evaluation Techniques</li>
                    <li>Cross-Validation</li>
                    <li>Hyperparameter Tuning</li>
                    <li>Overfitting & Underfitting</li>
                    <li>Bias-Variance Tradeoff</li>
                </ul>
                <div class="file-links">
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Notes/Module_4_ML_Concepts.pdf" class="file-link">📚
                        Notes</a>
                </div>
            </div>

            <!-- Module 5 -->
            <div class="module-card">
                <h2 class="module-title">Module 5: Neural Networks & Deep Learning</h2>
                <ul class="topics-list">
                    <li>Introduction to Neural Networks</li>
                    <li>Feedforward, CNN, RNN</li>
                    <li>Ensemble Methods (Bagging, Boosting)</li>
                    <li>Deep Learning Basics</li>
                    <li>Reinforcement Learning (MDP, Q-Learning)</li>
                </ul>
                <div class="file-links">
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Notes/Module_5_Neural_Networks.pdf" class="file-link">📚
                        Notes</a>
                </div>
            </div>

            <!-- General Resources -->
            <div class="module-card">
                <h2 class="module-title">📋 General Resources</h2>
                <ul class="topics-list">
                    <li>Comprehensive Question Bank</li>
                    <li>Exam Preparation Guide</li>
                    <li>Module-wise Important Questions</li>
                    <li>Critical Exam Insights</li>
                </ul>
                <div class="file-links">
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/Question_Banks/QB_General_ML.pdf"
                        class="file-link qb-link">❓ General QB</a>
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/EXAM_PREPARATION_GUIDE.md" class="file-link">📖 Exam
                        Guide</a>
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/MODULE_WISE_IMPORTANT_QUESTIONS.md" class="file-link">📝
                        Important Questions</a>
                    <a href="file:///d:/clg/Yen_Notes/sem4/IML/CRITICAL_EXAM_INSIGHTS.md" class="file-link">🎯 Exam
                        Insights</a>
                </div>
            </div>
        </div>

        <div class="stats-section">
            <h3 class="stats-title">🔥 Top Priority Topics (Based on Statistical Analysis)</h3>
            <div class="priority-list">
                <div class="priority-item">
                    <div class="priority-rank">#1</div>
                    <div class="priority-topic">Regression (Score: 275)</div>
                </div>
                <div class="priority-item">
                    <div class="priority-rank">#2</div>
                    <div class="priority-topic">Clustering (Score: 212)</div>
                </div>
                <div class="priority-item">
                    <div class="priority-rank">#3</div>
                    <div class="priority-topic">Classification (Score: 183)</div>
                </div>
                <div class="priority-item">
                    <div class="priority-rank">#4</div>
                    <div class="priority-topic">PCA (Score: 136)</div>
                </div>
                <div class="priority-item">
                    <div class="priority-rank">#5</div>
                    <div class="priority-topic">SVM (Score: 91)</div>
                </div>
                <div class="priority-item">
                    <div class="priority-rank">#6</div>
                    <div class="priority-topic">Random Forest (Score: 90)</div>
                </div>
            </div>
        </div>

        <footer class="footer">
            <p>📊 Analysis based on comprehensive study of 12 documents (7 Notes + 5 Question Banks)</p>
            <p>🎯 Focus 80% of study time on top 6 topics for maximum exam success</p>
        </footer>
    </div>
</body>

</html>
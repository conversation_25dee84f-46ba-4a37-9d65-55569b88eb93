<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 IML Exam Guide - Notion Style</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, 'Apple Color Emoji', Arial, sans-serif, 'Segoe UI Emoji', 'Segoe UI Symbol';
            background-color: #191919;
            color: #e9e9e7;
            line-height: 1.6;
            font-size: 16px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .page-title {
            font-size: 40px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .page-subtitle {
            font-size: 18px;
            color: #9b9a97;
            margin-bottom: 40px;
            font-weight: 400;
        }

        h1 {
            font-size: 32px;
            font-weight: 600;
            color: #ffffff;
            margin: 40px 0 16px 0;
            border-bottom: 1px solid #373737;
            padding-bottom: 8px;
        }

        h2 {
            font-size: 24px;
            font-weight: 600;
            color: #ffffff;
            margin: 32px 0 12px 0;
        }

        h3 {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            margin: 24px 0 8px 0;
        }

        h4 {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin: 20px 0 8px 0;
        }

        p {
            margin-bottom: 12px;
            color: #e9e9e7;
        }

        .block {
            margin-bottom: 16px;
        }

        .callout {
            background-color: #2f2f2f;
            border: 1px solid #373737;
            border-radius: 6px;
            padding: 16px;
            margin: 16px 0;
            border-left: 4px solid #0ea5e9;
        }

        .callout-fire {
            border-left-color: #ef4444;
            background-color: rgba(239, 68, 68, 0.1);
        }

        .callout-warning {
            border-left-color: #f59e0b;
            background-color: rgba(245, 158, 11, 0.1);
        }

        .callout-success {
            border-left-color: #10b981;
            background-color: rgba(16, 185, 129, 0.1);
        }

        ul,
        ol {
            margin-left: 24px;
            margin-bottom: 12px;
        }

        li {
            margin-bottom: 4px;
            color: #e9e9e7;
        }

        .bullet-list {
            list-style: none;
            margin-left: 0;
        }

        .bullet-list li {
            position: relative;
            padding-left: 20px;
            margin-bottom: 6px;
        }

        .bullet-list li::before {
            content: "•";
            color: #0ea5e9;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .checkbox-list {
            list-style: none;
            margin-left: 0;
        }

        .checkbox-list li {
            position: relative;
            padding-left: 28px;
            margin-bottom: 8px;
            cursor: pointer;
        }

        .checkbox-list li::before {
            content: "☐";
            position: absolute;
            left: 0;
            color: #9b9a97;
            font-size: 16px;
            transition: color 0.2s;
        }

        .checkbox-list li:hover::before {
            color: #0ea5e9;
        }

        .formula {
            background-color: #2f2f2f;
            border: 1px solid #373737;
            border-radius: 4px;
            padding: 12px 16px;
            margin: 8px 0;
            font-family: 'SF Mono', Monaco, Inconsolata, 'Roboto Mono', Consolas, 'Courier New', monospace;
            font-size: 14px;
            color: #f97316;
        }

        .table-container {
            overflow-x: auto;
            margin: 16px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            background-color: #2f2f2f;
            border-radius: 6px;
            overflow: hidden;
        }

        th,
        td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #373737;
        }

        th {
            background-color: #373737;
            font-weight: 600;
            color: #ffffff;
        }

        td {
            color: #e9e9e7;
        }

        .highlight {
            background-color: rgba(14, 165, 233, 0.2);
            padding: 2px 4px;
            border-radius: 3px;
            color: #7dd3fc;
        }

        .tag {
            display: inline-block;
            background-color: #374151;
            color: #d1d5db;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin: 2px 4px 2px 0;
        }

        .tag-priority {
            background-color: rgba(239, 68, 68, 0.2);
            color: #fca5a5;
        }

        .divider {
            height: 1px;
            background-color: #373737;
            margin: 32px 0;
            border: none;
        }

        .emoji {
            font-size: 1.2em;
            margin-right: 8px;
        }

        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #373737;
            border-radius: 4px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #0ea5e9, #06b6d4);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 16px;
            }

            .page-title {
                font-size: 32px;
            }

            h1 {
                font-size: 28px;
            }

            h2 {
                font-size: 22px;
            }
        }

        .scroll-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #0ea5e9;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .scroll-to-top.visible {
            opacity: 1;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="page-title">🎯 EXAM SURVIVAL GUIDE</div>
        <div class="page-subtitle">75 MARKS (Need 30 to Pass) • ⏰ 2 HOURS TO GO</div>

        <div class="callout callout-fire">
            <div class="section-header">
                <span class="emoji">🔥</span>
                <h2>GUARANTEED 30+ MARKS TOPICS</h2>
            </div>
            <p>Focus on these 5 topics and you'll easily pass your exam!</p>
        </div>

        <h1>1. REGRESSION (15-20 marks guaranteed)</h1>
        <span class="tag tag-priority">STUDY THIS FIRST!</span>

        <h3>Simple Linear Regression</h3>
        <div class="formula">y = mx + b</div>
        <ul class="bullet-list">
            <li>y = dependent variable (what we predict)</li>
            <li>x = independent variable (what we use to predict)</li>
            <li>m = slope (how much y changes when x increases by 1)</li>
            <li>b = intercept (value of y when x = 0)</li>
        </ul>

        <div class="callout">
            <h4>Example Question & Answer:</h4>
            <p><strong>Q:</strong> Explain simple linear regression with example</p>
            <p><strong>A:</strong></p>
            <ul class="bullet-list">
                <li>Simple linear regression finds the best line through data points</li>
                <li><strong>Example:</strong> Predicting house price (y) based on size (x)</li>
                <li>If size increases by 1 sq ft, price increases by $100 (slope = 100)</li>
                <li>Base price is $50,000 (intercept = 50,000)</li>
                <li><span class="highlight">Formula: Price = 100 × Size + 50,000</span></li>
            </ul>
        </div>

        <h3>R-squared (Coefficient of Determination)</h3>
        <div class="formula">R² = 1 - (SSres/SStot)</div>
        <ul class="bullet-list">
            <li><strong>Simple Explanation:</strong> How well the line fits the data</li>
            <li><strong>Range:</strong> 0 to 1</li>
            <li>0.7 = 70% of variation explained (Good fit)</li>
            <li>0.3 = 30% of variation explained (Poor fit)</li>
        </ul>

        <h3>Multiple Linear Regression</h3>
        <div class="formula">y = b₀ + b₁x₁ + b₂x₂ + ... + bₙxₙ</div>
        <p><strong>Example:</strong> House price = Base + (Size × coefficient) + (Bedrooms × coefficient)</p>

        <hr class="divider">

        <h1>2. CLASSIFICATION METRICS (10-15 marks guaranteed)</h1>

        <h3>Confusion Matrix - MEMORIZE THIS TABLE</h3>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th></th>
                        <th colspan="2">PREDICTED</th>
                    </tr>
                    <tr>
                        <th></th>
                        <th>Positive</th>
                        <th>Negative</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th rowspan="2">ACTUAL</th>
                        <td>TP</td>
                        <td>FN</td>
                    </tr>
                    <tr>
                        <td>FP</td>
                        <td>TN</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h3>Key Formulas - WRITE THESE IN EXAM</h3>
        <div class="block">
            <div class="formula">Accuracy = (TP + TN) / (TP + TN + FP + FN)</div>
            <p>"How many predictions were correct overall?"</p>
        </div>

        <div class="block">
            <div class="formula">Precision = TP / (TP + FP)</div>
            <p>"Of all positive predictions, how many were actually positive?"</p>
        </div>

        <div class="block">
            <div class="formula">Recall = TP / (TP + FN)</div>
            <p>"Of all actual positives, how many did we find?"</p>
        </div>

        <div class="block">
            <div class="formula">F1-Score = 2 × (Precision × Recall) / (Precision + Recall)</div>
            <p>"Balance between precision and recall"</p>
        </div>

        <div class="callout callout-success">
            <h4>Easy Example:</h4>
            <ul class="bullet-list">
                <li><strong>TP = 80</strong> (correctly predicted positive)</li>
                <li><strong>TN = 70</strong> (correctly predicted negative)</li>
                <li><strong>FP = 10</strong> (wrongly predicted positive)</li>
                <li><strong>FN = 40</strong> (wrongly predicted negative)</li>
            </ul>

            <h4>Calculations:</h4>
            <ul class="bullet-list">
                <li>Accuracy = (80+70)/(80+70+10+40) = 150/200 = 0.75 = 75%</li>
                <li>Precision = 80/(80+10) = 80/90 = 0.89 = 89%</li>
                <li>Recall = 80/(80+40) = 80/120 = 0.67 = 67%</li>
            </ul>
        </div>

        <hr class="divider">

        <h1>3. CLUSTERING (8-12 marks guaranteed)</h1>

        <h3>K-Means Clustering - 4 Simple Steps</h3>
        <ol>
            <li>Choose k (number of clusters)</li>
            <li>Place k centroids randomly</li>
            <li>Assign each point to nearest centroid</li>
            <li>Move centroids to center of assigned points</li>
            <li>Repeat steps 3-4 until centroids stop moving</li>
        </ol>

        <h3>Distance Formulas</h3>
        <div class="block">
            <div class="formula">Euclidean Distance: √[(x₁-x₂)² + (y₁-y₂)²]</div>
            <p>Like measuring straight line distance on map</p>
        </div>

        <div class="block">
            <div class="formula">Manhattan Distance: |x₁-x₂| + |y₁-y₂|</div>
            <p>Like walking in city blocks (only horizontal/vertical)</p>
        </div>

        <h3>Hierarchical Clustering</h3>
        <ul class="bullet-list">
            <li><strong>Agglomerative:</strong> Start with individual points, merge similar ones</li>
            <li><strong>Divisive:</strong> Start with all points together, split into groups</li>
            <li><strong>Dendrogram:</strong> Tree diagram showing how clusters merge</li>
        </ul>

        <hr class="divider">

        <h1>4. MACHINE LEARNING BASICS (5-8 marks guaranteed)</h1>

        <h3>Types of Machine Learning</h3>
        <div class="callout">
            <h4>1. Supervised Learning</h4>
            <ul class="bullet-list">
                <li>Has labeled data (input + correct output)</li>
                <li><strong>Examples:</strong> Email spam detection, house price prediction</li>
                <li><strong>Algorithms:</strong> Linear regression, decision trees, SVM</li>
            </ul>
        </div>

        <div class="callout">
            <h4>2. Unsupervised Learning</h4>
            <ul class="bullet-list">
                <li>No labeled data (only input, no correct output)</li>
                <li><strong>Examples:</strong> Customer segmentation, data compression</li>
                <li><strong>Algorithms:</strong> K-means clustering, PCA</li>
            </ul>
        </div>

        <div class="callout">
            <h4>3. Reinforcement Learning</h4>
            <ul class="bullet-list">
                <li>Learns through trial and error with rewards/penalties</li>
                <li><strong>Examples:</strong> Game playing, robot navigation</li>
            </ul>
        </div>

        <h3>Data Preprocessing Steps</h3>
        <ol>
            <li><strong>Data Cleaning:</strong> Remove missing values, duplicates, outliers</li>
            <li><strong>Feature Scaling:</strong>
                <ul class="bullet-list">
                    <li>Normalization: Scale to 0-1 range</li>
                    <li>Standardization: Mean=0, Standard deviation=1</li>
                </ul>
            </li>
            <li><strong>Train-Test Split:</strong> Usually 80% training, 20% testing</li>
        </ol>

        <hr class="divider">

        <h1>5. PCA (Principal Component Analysis) (5-8 marks)</h1>

        <h3>Simple Explanation</h3>
        <ul class="bullet-list">
            <li><strong>Purpose:</strong> Reduce number of features while keeping important information</li>
            <li><strong>Example:</strong> Instead of 100 features, use 10 most important ones</li>
        </ul>

        <h3>Key Concepts</h3>
        <ul class="bullet-list">
            <li><strong>Principal Components:</strong> New features that capture most variation</li>
            <li><strong>Eigenvalues:</strong> How much variation each component captures</li>
            <li><strong>Explained Variance Ratio:</strong> Percentage of information retained</li>
            <li><strong>Scree Plot:</strong> Graph to choose number of components (look for "elbow")</li>
        </ul>

        <h3>When to Use PCA</h3>
        <ul class="bullet-list">
            <li>Too many features (curse of dimensionality)</li>
            <li>Data visualization (reduce to 2D or 3D)</li>
            <li>Noise reduction</li>
            <li>Data compression</li>
        </ul>

        <hr class="divider">

        <div class="callout callout-warning">
            <div class="section-header">
                <span class="emoji">📝</span>
                <h2>EXAM STRATEGY FOR 75 MARKS</h2>
            </div>

            <h3>Question Distribution (Typical Pattern):</h3>
            <ul class="bullet-list">
                <li><strong>MCQs:</strong> 25 questions × 1 mark = 25 marks</li>
                <li><strong>Short Answers:</strong> 5 questions × 5 marks = 25 marks</li>
                <li><strong>Long Answers:</strong> 2 questions × 12.5 marks = 25 marks</li>
            </ul>

            <h3>How to Score 30+ Marks:</h3>
            <ol>
                <li><strong>MCQs (Target: 15/25 marks)</strong>
                    <ul class="bullet-list">
                        <li>Focus on definitions and basic concepts</li>
                        <li>Supervised vs Unsupervised</li>
                        <li>Algorithm characteristics</li>
                    </ul>
                </li>
                <li><strong>Short Answers (Target: 15/25 marks)</strong>
                    <ul class="bullet-list">
                        <li>Pick 3 questions you're confident about</li>
                        <li>Always include: Definition + Example + Application</li>
                    </ul>
                </li>
                <li><strong>Long Answers (Target: 8/25 marks)</strong>
                    <ul class="bullet-list">
                        <li>Choose 1 question you know well</li>
                        <li>Structure: Introduction → Explanation → Example → Conclusion</li>
                    </ul>
                </li>
            </ol>
        </div>

        <hr class="divider">

        <div class="callout callout-success">
            <div class="section-header">
                <span class="emoji">🎯</span>
                <h2>FINAL HOUR CHECKLIST</h2>
            </div>

            <h3>Must Know by Heart:</h3>
            <ul class="checkbox-list">
                <li>Linear regression formula: y = mx + b</li>
                <li>Accuracy formula: (TP + TN)/(TP + TN + FP + FN)</li>
                <li>K-means 4 steps</li>
                <li>3 types of ML with examples</li>
                <li>Confusion matrix layout</li>
                <li>PCA purpose and use cases</li>
            </ul>

            <h3>Practice Questions:</h3>
            <ol>
                <li>"Explain supervised vs unsupervised learning with examples" (5 marks)</li>
                <li>"Calculate accuracy, precision, recall from confusion matrix" (5 marks)</li>
                <li>"Describe K-means clustering algorithm" (5 marks)</li>
                <li>"What is linear regression? Give formula and example" (5 marks)</li>
            </ol>
        </div>

        <div class="callout callout-fire">
            <div class="section-header">
                <span class="emoji">🏆</span>
                <h2>YOU CAN DO THIS!</h2>
            </div>

            <h3>Focus on these 5 topics for the next 2 hours:</h3>
            <ol>
                <li><strong>Regression</strong> (30 minutes)</li>
                <li><strong>Classification Metrics</strong> (30 minutes)</li>
                <li><strong>Clustering</strong> (30 minutes)</li>
                <li><strong>ML Basics</strong> (20 minutes)</li>
                <li><strong>PCA</strong> (10 minutes)</li>
            </ol>

            <p><strong>Remember: You only need 30/75 marks to pass. These topics can easily give you 35+ marks!</strong>
            </p>

            <div style="text-align: center; margin-top: 20px; font-size: 18px;">
                <strong>GOOD LUCK! 🍀 YOU'VE GOT THIS! 💪</strong>
            </div>
        </div>

        <hr class="divider">

        <h1>📚 ADDITIONAL IMPORTANT TOPICS</h1>

        <h2>6. DECISION TREES (5-10 marks)</h2>

        <h3>How Decision Trees Work</h3>
        <ul class="bullet-list">
            <li>Creates a tree of if-then rules</li>
            <li><strong>Root Node:</strong> Starting point (top of tree)</li>
            <li><strong>Internal Nodes:</strong> Decision points (questions)</li>
            <li><strong>Leaf Nodes:</strong> Final predictions (bottom of tree)</li>
        </ul>

        <h3>Splitting Criteria</h3>
        <div class="block">
            <div class="formula">Gini Impurity: Gini = 1 - Σ(pi)²</div>
            <p>Measures how "impure" a node is. 0 = pure (all same class), 0.5 = most impure</p>
        </div>

        <div class="block">
            <div class="formula">Information Gain: IG = Entropy(parent) - Weighted_Avg(Entropy(children))</div>
            <p>How much information we gain by splitting</p>
        </div>

        <div class="block">
            <div class="formula">Entropy: Entropy = -Σ(pi × log2(pi))</div>
            <p>Measures randomness/disorder</p>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Advantages</th>
                        <th>Disadvantages</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Easy to understand and interpret</td>
                        <td>Prone to overfitting</td>
                    </tr>
                    <tr>
                        <td>No need for feature scaling</td>
                        <td>Can be unstable</td>
                    </tr>
                    <tr>
                        <td>Handles numerical and categorical data</td>
                        <td>Small data changes = different tree</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>7. RANDOM FOREST (5-8 marks)</h2>

        <div class="callout">
            <h3>What is Random Forest?</h3>
            <ul class="bullet-list">
                <li><strong>Ensemble Method:</strong> Combines multiple decision trees</li>
                <li><strong>Bagging:</strong> Bootstrap Aggregating - trains each tree on different data subset</li>
                <li><strong>Final Prediction:</strong> Majority vote (classification) or average (regression)</li>
            </ul>
        </div>

        <h3>Key Parameters</h3>
        <ul class="bullet-list">
            <li><strong>n_estimators:</strong> Number of trees (default: 100)</li>
            <li><strong>max_depth:</strong> Maximum depth of each tree</li>
            <li><strong>min_samples_split:</strong> Minimum samples to split a node</li>
        </ul>

        <h2>8. SUPPORT VECTOR MACHINES (SVM) (5-10 marks)</h2>

        <div class="callout callout-success">
            <h3>Core Concept</h3>
            <ul class="bullet-list">
                <li><strong>Goal:</strong> Find the best boundary (hyperplane) that separates classes</li>
                <li><strong>Support Vectors:</strong> Data points closest to the boundary</li>
                <li><strong>Margin:</strong> Distance between boundary and nearest points</li>
                <li><strong>Objective:</strong> Maximize the margin</li>
            </ul>
        </div>

        <h3>SVM Kernels</h3>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Kernel</th>
                        <th>Formula</th>
                        <th>Use Case</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Linear</td>
                        <td>K(x,y) = x·y</td>
                        <td>Linearly separable data</td>
                    </tr>
                    <tr>
                        <td>RBF (Gaussian)</td>
                        <td>K(x,y) = exp(-γ||x-y||²)</td>
                        <td>Non-linear data (most common)</td>
                    </tr>
                    <tr>
                        <td>Polynomial</td>
                        <td>K(x,y) = (x·y + c)^d</td>
                        <td>Polynomial relationships</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>9. MODEL EVALUATION & VALIDATION (8-12 marks)</h2>

        <h3>Cross-Validation</h3>
        <div class="callout">
            <h4>K-Fold Cross-Validation</h4>
            <ul class="bullet-list">
                <li>Split data into k parts</li>
                <li>Train on k-1 parts, test on 1 part</li>
                <li>Repeat k times, average the results</li>
                <li>Common: 5-fold or 10-fold</li>
            </ul>
        </div>

        <h3>Overfitting vs Underfitting</h3>
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Problem</th>
                        <th>Description</th>
                        <th>Solutions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="tag">Overfitting</span></td>
                        <td>Model memorizes training data<br>High training accuracy, low test accuracy</td>
                        <td>More data, regularization, simpler model</td>
                    </tr>
                    <tr>
                        <td><span class="tag">Underfitting</span></td>
                        <td>Model too simple to capture patterns<br>Low training and test accuracy</td>
                        <td>More complex model, more features</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h2>🧮 COMPLETE FORMULA SHEET</h2>

        <div class="callout callout-warning">
            <h3>Regression Formulas</h3>
            <div class="formula">Simple Linear Regression: y = mx + b</div>
            <div class="formula">Multiple Linear Regression: y = b₀ + b₁x₁ + b₂x₂ + ... + bₙxₙ</div>
            <div class="formula">Mean Squared Error (MSE): MSE = Σ(yi - ŷi)²/n</div>
            <div class="formula">R-squared: R² = 1 - (SSres/SStot)</div>
        </div>

        <div class="callout callout-warning">
            <h3>Classification Formulas</h3>
            <div class="formula">Accuracy: (TP + TN)/(TP + TN + FP + FN)</div>
            <div class="formula">Precision: TP/(TP + FP)</div>
            <div class="formula">Recall: TP/(TP + FN)</div>
            <div class="formula">F1-Score: 2 × (Precision × Recall)/(Precision + Recall)</div>
        </div>

        <div class="callout callout-warning">
            <h3>Distance Formulas</h3>
            <div class="formula">Euclidean Distance: d = √[(x₁-x₂)² + (y₁-y₂)²]</div>
            <div class="formula">Manhattan Distance: d = |x₁-x₂| + |y₁-y₂|</div>
            <div class="formula">Cosine Similarity: cos(θ) = (A·B)/(||A|| × ||B||)</div>
        </div>

        <h2>📋 ALGORITHM COMPARISON TABLE</h2>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>Algorithm</th>
                        <th>Type</th>
                        <th>Pros</th>
                        <th>Cons</th>
                        <th>Use Cases</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Linear Regression</td>
                        <td>Supervised</td>
                        <td>Simple, interpretable</td>
                        <td>Assumes linear relationship</td>
                        <td>House prices, sales</td>
                    </tr>
                    <tr>
                        <td>Decision Tree</td>
                        <td>Supervised</td>
                        <td>Easy to understand</td>
                        <td>Prone to overfitting</td>
                        <td>Medical diagnosis</td>
                    </tr>
                    <tr>
                        <td>Random Forest</td>
                        <td>Supervised</td>
                        <td>Reduces overfitting</td>
                        <td>Less interpretable</td>
                        <td>General classification</td>
                    </tr>
                    <tr>
                        <td>SVM</td>
                        <td>Supervised</td>
                        <td>Works with high dimensions</td>
                        <td>Slow on large datasets</td>
                        <td>Text classification</td>
                    </tr>
                    <tr>
                        <td>K-Means</td>
                        <td>Unsupervised</td>
                        <td>Simple, efficient</td>
                        <td>Need to choose k</td>
                        <td>Customer segmentation</td>
                    </tr>
                    <tr>
                        <td>PCA</td>
                        <td>Unsupervised</td>
                        <td>Reduces dimensions</td>
                        <td>Loses interpretability</td>
                        <td>Data visualization</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="callout callout-fire">
            <div class="section-header">
                <span class="emoji">🚨</span>
                <h2>EMERGENCY EXAM TIPS</h2>
            </div>

            <h3>If You Don't Know an Answer:</h3>
            <ol>
                <li><strong>Write what you know</strong> - partial marks are better than zero</li>
                <li><strong>Use examples</strong> - even if theory is weak, examples show understanding</li>
                <li><strong>Draw diagrams</strong> - confusion matrix, clustering visualization</li>
                <li><strong>Mention applications</strong> - real-world uses of algorithms</li>
            </ol>

            <h3>Time Management:</h3>
            <ul class="bullet-list">
                <li><strong>MCQs:</strong> 30 minutes (1.2 min per question)</li>
                <li><strong>Short Answers:</strong> 50 minutes (10 min per question)</li>
                <li><strong>Long Answers:</strong> 40 minutes (20 min per question)</li>
            </ul>

            <h3>Common Mistakes to Avoid:</h3>
            <ul class="bullet-list">
                <li>Don't confuse precision and recall</li>
                <li>Don't mix up supervised and unsupervised</li>
                <li>Always show calculations step by step</li>
                <li>Don't leave questions blank</li>
            </ul>
        </div>

        <div class="callout callout-success">
            <div class="section-header">
                <span class="emoji">🎯</span>
                <h2>FINAL EXAM CHECKLIST</h2>
            </div>

            <h3>Must Memorize (Write on paper during exam):</h3>
            <ul class="checkbox-list">
                <li>All classification metric formulas</li>
                <li>Linear regression formula and R²</li>
                <li>K-means algorithm steps</li>
                <li>Distance formulas (Euclidean, Manhattan)</li>
                <li>Confusion matrix layout</li>
                <li>Types of ML with examples</li>
                <li>Decision tree splitting criteria</li>
                <li>Feature scaling formulas</li>
            </ul>

            <h3>Must Understand:</h3>
            <ul class="checkbox-list">
                <li>When to use each algorithm</li>
                <li>Overfitting vs underfitting</li>
                <li>Bias-variance tradeoff</li>
                <li>Cross-validation purpose</li>
                <li>Ensemble methods benefits</li>
                <li>PCA applications</li>
                <li>SVM kernel types</li>
            </ul>
        </div>

        <div
            style="text-align: center; margin: 40px 0; padding: 30px; background: linear-gradient(135deg, #0ea5e9, #06b6d4); border-radius: 12px; color: white;">
            <h2 style="margin-bottom: 16px; color: white;">🚀 YOU'RE NOW FULLY PREPARED!</h2>
            <p style="font-size: 18px; margin-bottom: 8px;">THIS COVERS EVERYTHING! 💪</p>
            <p style="font-size: 16px; opacity: 0.9;">Remember: You only need 30/75 marks to pass!</p>
        </div>
    </div>

    <button class="scroll-to-top" onclick="scrollToTop()">↑</button>

    <script>
        // Show/hide scroll to top button
        window.addEventListener('scroll', function () {
            const scrollButton = document.querySelector('.scroll-to-top');
            if (window.pageYOffset > 300) {
                scrollButton.classList.add('visible');
            } else {
                scrollButton.classList.remove('visible');
            }
        });

        // Scroll to top function
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Interactive checkboxes
        document.querySelectorAll('.checkbox-list li').forEach(item => {
            item.addEventListener('click', function () {
                if (this.style.textDecoration === 'line-through') {
                    this.style.textDecoration = 'none';
                    this.style.opacity = '1';
                    this.querySelector('::before') ? this.style.setProperty('--checkbox', '☐') : null;
                } else {
                    this.style.textDecoration = 'line-through';
                    this.style.opacity = '0.6';
                }
            });
        });
    </script>
</body>

</html>
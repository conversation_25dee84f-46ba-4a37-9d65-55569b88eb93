<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Introduction to Machine Learning - Mobile Study Hub</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #e0e0e0;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 15px;
        }

        header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
            border-bottom: 2px solid #333;
        }

        h1 {
            font-size: 2rem;
            color: #64ffda;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .subtitle {
            color: #888;
            font-size: 1rem;
            font-weight: 300;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .module-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .module-card:hover {
            transform: translateY(-3px);
            border-color: #64ffda;
            box-shadow: 0 8px 25px rgba(100, 255, 218, 0.1);
        }

        .module-title {
            color: #64ffda;
            font-size: 1.2rem;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .topics-list {
            list-style: none;
            margin-bottom: 20px;
        }

        .topics-list li {
            padding: 4px 0;
            color: #ccc;
            font-size: 0.9rem;
        }

        .topics-list li:before {
            content: "▸ ";
            color: #64ffda;
            font-weight: bold;
        }

        .file-links {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-top: 15px;
        }

        .file-link {
            display: block;
            padding: 12px 16px;
            background: rgba(100, 255, 218, 0.1);
            border: 1px solid #64ffda;
            border-radius: 8px;
            color: #64ffda;
            text-decoration: none;
            font-size: 0.9rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .file-link:hover {
            background: #64ffda;
            color: #0f0f23;
            transform: scale(1.02);
        }

        .qb-link {
            background: rgba(255, 107, 107, 0.1);
            border-color: #ff6b6b;
            color: #ff6b6b;
        }

        .qb-link:hover {
            background: #ff6b6b;
            color: #0f0f23;
        }

        .stats-section {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
            padding: 20px;
            margin-top: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stats-title {
            color: #64ffda;
            font-size: 1.1rem;
            margin-bottom: 15px;
        }

        .priority-list {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;
        }

        .priority-item {
            background: rgba(100, 255, 218, 0.05);
            padding: 10px 14px;
            border-radius: 8px;
            border-left: 3px solid #64ffda;
        }

        .priority-rank {
            color: #64ffda;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .priority-topic {
            color: #fff;
            font-size: 0.9rem;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding: 15px;
            color: #666;
            border-top: 1px solid #333;
            font-size: 0.8rem;
        }

        .server-info {
            background: rgba(100, 255, 218, 0.1);
            border: 1px solid #64ffda;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .server-info h3 {
            color: #64ffda;
            margin-bottom: 10px;
        }

        .ip-address {
            font-family: monospace;
            background: rgba(0, 0, 0, 0.3);
            padding: 5px 10px;
            border-radius: 4px;
            color: #00ff00;
            margin: 5px;
            display: inline-block;
        }

        @media (min-width: 768px) {
            .modules-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            }
            
            .file-links {
                flex-direction: row;
                flex-wrap: wrap;
            }
            
            .priority-list {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="server-info">
            <h3>📱 Mobile Access Instructions</h3>
            <p>Connect your mobile to the same WiFi network and visit:</p>
            <div class="ip-address">http://************:8000/mobile.html</div>
            <div class="ip-address">http://*************:8000/mobile.html</div>
            <div class="ip-address">http://*************:8000/mobile.html</div>
            <p style="font-size: 0.8rem; margin-top: 10px;">Try each IP address until one works</p>
        </div>

        <header>
            <h1>Introduction to Machine Learning</h1>
            <p class="subtitle">BCA 4th Semester • Mobile Study Hub</p>
        </header>

        <div class="modules-grid">
            <!-- Module 1 -->
            <div class="module-card">
                <h2 class="module-title">Module 1: Introduction to ML</h2>
                <ul class="topics-list">
                    <li>What is Machine Learning?</li>
                    <li>Types of ML (Supervised, Unsupervised, Reinforcement)</li>
                    <li>Data Types & Preprocessing</li>
                    <li>Data Cleaning & Wrangling</li>
                    <li>Data Exploration & Visualization</li>
                </ul>
                <div class="file-links">
                    <a href="Notes/Module_1_Introduction_to_ML.pdf" class="file-link" target="_blank">📚 Notes</a>
                    <a href="Question_Banks/QB_Module_1.pdf" class="file-link qb-link" target="_blank">❓ Question Bank</a>
                </div>
            </div>

            <!-- Module 2 Part 1 -->
            <div class="module-card">
                <h2 class="module-title">Module 2: Supervised Learning - Part 1</h2>
                <ul class="topics-list">
                    <li>Linear Regression (Simple & Multiple)</li>
                    <li>Residual Analysis</li>
                    <li>R-squared & Adjusted R-squared</li>
                    <li>Feature Scaling & Normalization</li>
                    <li>Maximum Likelihood Estimation</li>
                </ul>
                <div class="file-links">
                    <a href="Notes/Module_2_Part_1.pdf" class="file-link" target="_blank">📚 Notes</a>
                    <a href="Question_Banks/QB_Module_2_Part_1.pdf" class="file-link qb-link" target="_blank">❓ Question Bank</a>
                </div>
            </div>

            <!-- Module 2 Part 2 -->
            <div class="module-card">
                <h2 class="module-title">Module 2: Supervised Learning - Part 2</h2>
                <ul class="topics-list">
                    <li>Classification & Confusion Matrix</li>
                    <li>ROC Curve & Performance Metrics</li>
                    <li>Decision Trees & CART</li>
                    <li>Random Forest & Ensemble Methods</li>
                    <li>Support Vector Machines (SVM)</li>
                </ul>
                <div class="file-links">
                    <a href="Notes/Module_2_Part_2.pdf" class="file-link" target="_blank">📚 Notes</a>
                    <a href="Question_Banks/QB_Module_2_Part_2.pdf" class="file-link qb-link" target="_blank">❓ Question Bank</a>
                </div>
            </div>

            <!-- Module 3 Part 1 -->
            <div class="module-card">
                <h2 class="module-title">Module 3: Unsupervised Learning - Part 1</h2>
                <ul class="topics-list">
                    <li>Clustering (K-Means & Hierarchical)</li>
                    <li>Distance Measures</li>
                    <li>Principal Component Analysis (PCA)</li>
                    <li>Eigenvectors & Eigenvalues</li>
                    <li>Dimensionality Reduction</li>
                </ul>
                <div class="file-links">
                    <a href="Notes/Module_3_Part_1.pdf" class="file-link" target="_blank">📚 Notes</a>
                    <a href="Question_Banks/QB_Module_3_Part_1.pdf" class="file-link qb-link" target="_blank">❓ Question Bank</a>
                </div>
            </div>

            <!-- Module 3 Part 2 -->
            <div class="module-card">
                <h2 class="module-title">Module 3: Unsupervised Learning - Part 2</h2>
                <ul class="topics-list">
                    <li>Anomaly Detection</li>
                    <li>Recommendation Systems</li>
                    <li>Collaborative Filtering</li>
                    <li>Content-based Filtering</li>
                    <li>Matrix Factorization</li>
                </ul>
                <div class="file-links">
                    <a href="Notes/Module_3_Part_2.pdf" class="file-link" target="_blank">📚 Notes</a>
                </div>
            </div>

            <!-- Module 4 -->
            <div class="module-card">
                <h2 class="module-title">Module 4: Advanced ML Concepts</h2>
                <ul class="topics-list">
                    <li>Model Evaluation Techniques</li>
                    <li>Cross-Validation</li>
                    <li>Hyperparameter Tuning</li>
                    <li>Overfitting & Underfitting</li>
                    <li>Bias-Variance Tradeoff</li>
                </ul>
                <div class="file-links">
                    <a href="Notes/Module_4_ML_Concepts.pdf" class="file-link" target="_blank">📚 Notes</a>
                </div>
            </div>

            <!-- Module 5 -->
            <div class="module-card">
                <h2 class="module-title">Module 5: Neural Networks & Deep Learning</h2>
                <ul class="topics-list">
                    <li>Introduction to Neural Networks</li>
                    <li>Feedforward, CNN, RNN</li>
                    <li>Ensemble Methods (Bagging, Boosting)</li>
                    <li>Deep Learning Basics</li>
                    <li>Reinforcement Learning (MDP, Q-Learning)</li>
                </ul>
                <div class="file-links">
                    <a href="Notes/Module_5_Neural_Networks.pdf" class="file-link" target="_blank">📚 Notes</a>
                </div>
            </div>
        </div>

        <div class="stats-section">
            <h3 class="stats-title">🔥 Top Priority Topics</h3>
            <div class="priority-list">
                <div class="priority-item">
                    <div class="priority-rank">#1</div>
                    <div class="priority-topic">Regression (Score: 275)</div>
                </div>
                <div class="priority-item">
                    <div class="priority-rank">#2</div>
                    <div class="priority-topic">Clustering (Score: 212)</div>
                </div>
                <div class="priority-item">
                    <div class="priority-rank">#3</div>
                    <div class="priority-topic">Classification (Score: 183)</div>
                </div>
                <div class="priority-item">
                    <div class="priority-rank">#4</div>
                    <div class="priority-topic">PCA (Score: 136)</div>
                </div>
                <div class="priority-item">
                    <div class="priority-rank">#5</div>
                    <div class="priority-topic">SVM (Score: 91)</div>
                </div>
                <div class="priority-item">
                    <div class="priority-rank">#6</div>
                    <div class="priority-topic">Random Forest (Score: 90)</div>
                </div>
            </div>
        </div>

        <footer class="footer">
            <p>📊 Analysis based on comprehensive study of 12 documents</p>
            <p>🎯 Focus 80% of study time on top 6 topics for maximum exam success</p>
        </footer>
    </div>
</body>
</html>

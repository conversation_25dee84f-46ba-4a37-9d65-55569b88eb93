#!/usr/bin/env python3
"""
ML Topic Analysis Script
Analyzes all ML documents to find the most important exam topics
"""

import os
import re
from collections import Counter, defaultdict
import matplotlib.pyplot as plt

def extract_key_terms(text):
    """Extract ML-related key terms from text"""
    
    # Define important ML terms to look for
    ml_terms = [
        # Core ML Concepts
        'machine learning', 'supervised learning', 'unsupervised learning', 'reinforcement learning',
        'classification', 'regression', 'clustering', 'dimensionality reduction',
        
        # Algorithms
        'linear regression', 'multiple linear regression', 'decision tree', 'random forest',
        'support vector machine', 'svm', 'k-means', 'hierarchical clustering',
        'pca', 'principal component analysis', 'naive bayes', 'neural network',
        
        # Evaluation Metrics
        'accuracy', 'precision', 'recall', 'f1-score', 'confusion matrix',
        'roc curve', 'auc', 'mse', 'mean squared error', 'r-squared', 'adjusted r-squared',
        
        # Data Processing
        'data preprocessing', 'data cleaning', 'feature scaling', 'normalization',
        'standardization', 'feature engineering', 'feature selection', 'one-hot encoding',
        'train test split', 'cross validation', 'overfitting', 'underfitting',
        
        # Distance Metrics
        'euclidean distance', 'manhattan distance', 'cosine similarity', 'hamming distance',
        
        # Advanced Topics
        'anomaly detection', 'recommendation system', 'collaborative filtering',
        'content-based filtering', 'matrix factorization', 'ensemble methods',
        'hyperparameter tuning', 'grid search', 'bagging', 'boosting',
        
        # Statistical Concepts
        'maximum likelihood estimation', 'mle', 'residual analysis', 'gini impurity',
        'information gain', 'entropy', 'variance', 'bias', 'correlation',
        
        # Applications
        'fraud detection', 'spam detection', 'image recognition', 'natural language processing',
        'nlp', 'healthcare', 'finance', 'autonomous systems'
    ]
    
    # Convert to lowercase for case-insensitive matching
    text_lower = text.lower()
    
    # Count occurrences of each term
    term_counts = {}
    for term in ml_terms:
        # Use word boundaries to avoid partial matches
        pattern = r'\b' + re.escape(term) + r'\b'
        count = len(re.findall(pattern, text_lower))
        if count > 0:
            term_counts[term] = count
    
    return term_counts

def analyze_file(filepath):
    """Analyze a single file and return term frequencies"""
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        return extract_key_terms(content)
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
        return {}

def main():
    """Main analysis function"""
    
    # Directories to analyze
    analysis_dir = "analysis"
    
    if not os.path.exists(analysis_dir):
        print("Analysis directory not found!")
        return
    
    # Separate notes and question banks
    notes_files = []
    qb_files = []
    
    for filename in os.listdir(analysis_dir):
        if filename.endswith('.txt'):
            filepath = os.path.join(analysis_dir, filename)
            if filename.startswith('QB_'):
                qb_files.append(filepath)
            else:
                notes_files.append(filepath)
    
    print("=== ML TOPIC FREQUENCY ANALYSIS ===\n")
    
    # Analyze notes
    print("📚 ANALYZING NOTES...")
    notes_terms = Counter()
    for filepath in notes_files:
        print(f"  Processing: {os.path.basename(filepath)}")
        file_terms = analyze_file(filepath)
        notes_terms.update(file_terms)
    
    # Analyze question banks
    print("\n❓ ANALYZING QUESTION BANKS...")
    qb_terms = Counter()
    for filepath in qb_files:
        print(f"  Processing: {os.path.basename(filepath)}")
        file_terms = analyze_file(filepath)
        qb_terms.update(file_terms)
    
    # Combine and analyze
    all_terms = Counter()
    all_terms.update(notes_terms)
    all_terms.update(qb_terms)
    
    # Calculate importance scores (QB terms weighted higher for exam relevance)
    importance_scores = Counter()
    for term in all_terms:
        notes_freq = notes_terms.get(term, 0)
        qb_freq = qb_terms.get(term, 0)
        # Weight QB mentions higher (2x) as they're more exam-relevant
        importance_score = notes_freq + (qb_freq * 2)
        importance_scores[term] = importance_score
    
    # Display results
    print("\n" + "="*60)
    print("🎯 TOP 25 MOST IMPORTANT EXAM TOPICS")
    print("="*60)
    print("(Score = Notes frequency + 2×QB frequency)")
    print()
    
    for i, (term, score) in enumerate(importance_scores.most_common(25), 1):
        notes_count = notes_terms.get(term, 0)
        qb_count = qb_terms.get(term, 0)
        print(f"{i:2d}. {term.upper():<30} Score: {score:3d} (Notes: {notes_count:2d}, QB: {qb_count:2d})")
    
    # Category-wise analysis
    print("\n" + "="*60)
    print("📊 CATEGORY-WISE BREAKDOWN")
    print("="*60)
    
    categories = {
        'Core ML Types': ['supervised learning', 'unsupervised learning', 'reinforcement learning'],
        'Algorithms': ['linear regression', 'decision tree', 'random forest', 'svm', 'k-means', 'pca'],
        'Evaluation': ['accuracy', 'precision', 'recall', 'f1-score', 'confusion matrix', 'roc curve'],
        'Data Processing': ['data preprocessing', 'feature scaling', 'normalization', 'standardization'],
        'Advanced Topics': ['anomaly detection', 'recommendation system', 'ensemble methods']
    }
    
    for category, terms in categories.items():
        print(f"\n{category}:")
        category_scores = []
        for term in terms:
            if term in importance_scores:
                score = importance_scores[term]
                category_scores.append((term, score))
        
        # Sort by score and display
        category_scores.sort(key=lambda x: x[1], reverse=True)
        for term, score in category_scores:
            print(f"  • {term:<25} Score: {score}")
    
    print("\n" + "="*60)
    print("💡 EXAM PREPARATION RECOMMENDATIONS")
    print("="*60)
    
    # Get top topics by category
    top_topics = importance_scores.most_common(15)
    
    print("\n🔥 MUST-KNOW TOPICS (Top Priority):")
    for term, score in top_topics[:8]:
        print(f"  ✓ {term.title()}")
    
    print("\n⭐ HIGH PRIORITY TOPICS:")
    for term, score in top_topics[8:15]:
        print(f"  • {term.title()}")

if __name__ == "__main__":
    main()

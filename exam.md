# 🎯 EXAM SURVIVAL GUIDE - 75 MARKS (Need 30 to Pass)
## ⏰ 2 HOURS TO GO - FOCUS ON THESE ONLY!

---

## 🔥 **GUARANTEED 30+ MARKS TOPICS**

### **1. REGRESSION (15-20 marks guaranteed) - STUDY THIS FIRST!**

#### **Simple Linear Regression**
- **Formula**: `y = mx + b`
  - `y` = dependent variable (what we predict)
  - `x` = independent variable (what we use to predict)
  - `m` = slope (how much y changes when x increases by 1)
  - `b` = intercept (value of y when x = 0)

#### **Example Question & Answer:**
**Q: Explain simple linear regression with example**
**A:** 
- Simple linear regression finds the best line through data points
- **Example**: Predicting house price (y) based on size (x)
- If size increases by 1 sq ft, price increases by $100 (slope = 100)
- Base price is $50,000 (intercept = 50,000)
- **Formula**: Price = 100 × Size + 50,000

#### **R-squared (Coefficient of Determination)**
- **Formula**: `R² = 1 - (SSres/SStot)`
- **Simple Explanation**: How well the line fits the data
- **Range**: 0 to 1
- **0.7 = 70% of variation explained** (Good fit)
- **0.3 = 30% of variation explained** (Poor fit)

#### **Multiple Linear Regression**
- **Formula**: `y = b₀ + b₁x₁ + b₂x₂ + ... + bₙxₙ`
- **Example**: House price = Base + (Size × coefficient) + (Bedrooms × coefficient)

---

### **2. CLASSIFICATION METRICS (10-15 marks guaranteed)**

#### **Confusion Matrix - MEMORIZE THIS TABLE**
```
                 PREDICTED
              Positive  Negative
ACTUAL Positive   TP      FN
       Negative   FP      TN
```

#### **Key Formulas - WRITE THESE IN EXAM**
1. **Accuracy** = `(TP + TN) / (TP + TN + FP + FN)`
   - "How many predictions were correct overall?"

2. **Precision** = `TP / (TP + FP)`
   - "Of all positive predictions, how many were actually positive?"

3. **Recall** = `TP / (TP + FN)`
   - "Of all actual positives, how many did we find?"

4. **F1-Score** = `2 × (Precision × Recall) / (Precision + Recall)`
   - "Balance between precision and recall"

#### **Easy Example:**
- **TP = 80** (correctly predicted positive)
- **TN = 70** (correctly predicted negative)  
- **FP = 10** (wrongly predicted positive)
- **FN = 40** (wrongly predicted negative)

**Calculations:**
- Accuracy = (80+70)/(80+70+10+40) = 150/200 = 0.75 = 75%
- Precision = 80/(80+10) = 80/90 = 0.89 = 89%
- Recall = 80/(80+40) = 80/120 = 0.67 = 67%

---

### **3. CLUSTERING (8-12 marks guaranteed)**

#### **K-Means Clustering - 4 Simple Steps**
1. **Choose k** (number of clusters)
2. **Place k centroids** randomly
3. **Assign each point** to nearest centroid
4. **Move centroids** to center of assigned points
5. **Repeat steps 3-4** until centroids stop moving

#### **Distance Formulas**
1. **Euclidean Distance**: `√[(x₁-x₂)² + (y₁-y₂)²]`
   - Like measuring straight line distance on map

2. **Manhattan Distance**: `|x₁-x₂| + |y₁-y₂|`
   - Like walking in city blocks (only horizontal/vertical)

#### **Hierarchical Clustering**
- **Agglomerative**: Start with individual points, merge similar ones
- **Divisive**: Start with all points together, split into groups
- **Dendrogram**: Tree diagram showing how clusters merge

---

### **4. MACHINE LEARNING BASICS (5-8 marks guaranteed)**

#### **Types of Machine Learning**
1. **Supervised Learning**
   - Has labeled data (input + correct output)
   - **Examples**: Email spam detection, house price prediction
   - **Algorithms**: Linear regression, decision trees, SVM

2. **Unsupervised Learning**
   - No labeled data (only input, no correct output)
   - **Examples**: Customer segmentation, data compression
   - **Algorithms**: K-means clustering, PCA

3. **Reinforcement Learning**
   - Learns through trial and error with rewards/penalties
   - **Examples**: Game playing, robot navigation

#### **Data Preprocessing Steps**
1. **Data Cleaning**: Remove missing values, duplicates, outliers
2. **Feature Scaling**: 
   - **Normalization**: Scale to 0-1 range
   - **Standardization**: Mean=0, Standard deviation=1
3. **Train-Test Split**: Usually 80% training, 20% testing

---

### **5. PCA (Principal Component Analysis) (5-8 marks)**

#### **Simple Explanation**
- **Purpose**: Reduce number of features while keeping important information
- **Example**: Instead of 100 features, use 10 most important ones

#### **Key Concepts**
- **Principal Components**: New features that capture most variation
- **Eigenvalues**: How much variation each component captures
- **Explained Variance Ratio**: Percentage of information retained
- **Scree Plot**: Graph to choose number of components (look for "elbow")

#### **When to Use PCA**
- Too many features (curse of dimensionality)
- Data visualization (reduce to 2D or 3D)
- Noise reduction
- Data compression

---

## 📝 **EXAM STRATEGY FOR 75 MARKS**

### **Question Distribution (Typical Pattern):**
- **MCQs**: 25 questions × 1 mark = 25 marks
- **Short Answers**: 5 questions × 5 marks = 25 marks  
- **Long Answers**: 2 questions × 12.5 marks = 25 marks

### **How to Score 30+ Marks:**
1. **MCQs (Target: 15/25 marks)**
   - Focus on definitions and basic concepts
   - Supervised vs Unsupervised
   - Algorithm characteristics

2. **Short Answers (Target: 15/25 marks)**
   - Pick 3 questions you're confident about
   - Always include: Definition + Example + Application

3. **Long Answers (Target: 8/25 marks)**
   - Choose 1 question you know well
   - Structure: Introduction → Explanation → Example → Conclusion

---

## ⚡ **LAST-MINUTE MEMORIZATION**

### **Definitions (For MCQs)**
- **Machine Learning**: Computer systems that learn from data without explicit programming
- **Supervised Learning**: Learning with labeled examples
- **Clustering**: Grouping similar data points together
- **Regression**: Predicting continuous numerical values
- **Classification**: Predicting categories or classes
- **Overfitting**: Model memorizes training data but fails on new data
- **Cross-validation**: Testing model on different data subsets

### **Quick Algorithm Comparison**
| Algorithm | Type | Use Case | Key Feature |
|-----------|------|----------|-------------|
| Linear Regression | Supervised | Predict numbers | Finds best line |
| K-Means | Unsupervised | Group data | Creates k clusters |
| Decision Tree | Supervised | Classification | Creates if-then rules |
| PCA | Unsupervised | Reduce features | Keeps important info |
| SVM | Supervised | Classification | Maximizes margin |

---

## 🎯 **FINAL HOUR CHECKLIST**

### **Must Know by Heart:**
- [ ] Linear regression formula: y = mx + b
- [ ] Accuracy formula: (TP + TN)/(TP + TN + FP + FN)
- [ ] K-means 4 steps
- [ ] 3 types of ML with examples
- [ ] Confusion matrix layout
- [ ] PCA purpose and use cases

### **Practice Questions:**
1. "Explain supervised vs unsupervised learning with examples" (5 marks)
2. "Calculate accuracy, precision, recall from confusion matrix" (5 marks)
3. "Describe K-means clustering algorithm" (5 marks)
4. "What is linear regression? Give formula and example" (5 marks)

---

## 🚨 **EMERGENCY EXAM TIPS**

### **If You Don't Know an Answer:**
1. **Write what you know** - partial marks are better than zero
2. **Use examples** - even if theory is weak, examples show understanding
3. **Draw diagrams** - confusion matrix, clustering visualization
4. **Mention applications** - real-world uses of algorithms

### **Time Management:**
- **MCQs**: 30 minutes (1.2 min per question)
- **Short Answers**: 50 minutes (10 min per question)
- **Long Answers**: 40 minutes (20 min per question)

### **Common Mistakes to Avoid:**
- Don't confuse precision and recall
- Don't mix up supervised and unsupervised
- Always show calculations step by step
- Don't leave questions blank

---

## 🏆 **YOU CAN DO THIS!**

**Focus on these 5 topics for the next 2 hours:**
1. **Regression** (30 minutes)
2. **Classification Metrics** (30 minutes)  
3. **Clustering** (30 minutes)
4. **ML Basics** (20 minutes)
5. **PCA** (10 minutes)

**Remember: You only need 30/75 marks to pass. These topics can easily give you 35+ marks!**

**GOOD LUCK! 🍀 YOU'VE GOT THIS! 💪**
